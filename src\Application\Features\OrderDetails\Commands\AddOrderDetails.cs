using DispatchR.Requests.Send;
using DNTPersianUtils.Core;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;
using Zify.Settlement.Application.Infrastructure.Services.Users;

namespace Zify.Settlement.Application.Features.OrderDetails.Commands;

public sealed record AddOrderDetailsResponse(List<OrderDetailResponse> OrderDetails);

public sealed class AddOrderDetailsController : ApiControllerBase
{
    [HttpPost("{orderId:guid}/add-order-details")]
    [Authorize("write")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AddOrderDetails(
        [FromRoute] Guid orderId,
        [FromBody] AddOrderDetailsCommand request)
    {
        request.OrderId = orderId;
        var result = await Mediator.Send(request, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record AddOrderDetailsCommand(List<OrderDetailItem> Items)
    : IRequest<AddOrderDetailsCommand, Task<ErrorOr<AddOrderDetailsResponse>>>
{
    [JsonIgnore]
    public Guid OrderId { get; set; }
}

public sealed record OrderDetailItem(
    decimal Amount,
    string Iban,
    string? Description,
    string? Mobile,
    string? NationalId);

public sealed class AddOrderDetailsCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    IDateTime dateTime,
    IWageCalculatorService wageCalculatorService,
    IIbanInquiryService ibanInquiryService)
    : IRequestHandler<AddOrderDetailsCommand, Task<ErrorOr<AddOrderDetailsResponse>>>
{
    public async Task<ErrorOr<AddOrderDetailsResponse>> Handle(
        AddOrderDetailsCommand request,
        CancellationToken cancellationToken)
    {
        var order = await dbContext.Orders
            .AsTracking()
            .Where(o => o.Id == request.OrderId)
            .Where(o => o.CreatedBy == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (order is null)
            return Error.NotFound(description: "کد درخواست نامعتبر می‌باشد");

        if (order.Status != OrderStatus.Draft)
            return Error.Forbidden(description: "با توجه به وضعیت درخواست افزودن تسویه امکان پذیر نیست");

        var userConfig = await dbContext.UserConfigs
            .AsNoTracking()
            .Where(uc => uc.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);
        if (userConfig is null) return Error.NotFound(description: "اطلاعات کاربری یافت نشد");

        var exceedLimitation = await ExceedDailyTransferLimitation(
            userConfig,
            request.Items.Sum(x => x.Amount),
            cancellationToken);
        if (exceedLimitation.IsError)
            return exceedLimitation.Errors;

        var createdOrderDetails = new List<OrderDetailResponse>();

        foreach (var item in request.Items)
        {
            var fullNameOrIban = await TryValidateIbanAndAccountStatus(item.Iban);
            if (fullNameOrIban.IsError)
                return fullNameOrIban.Errors;

            var orderDetail = OrderDetail.Create(
                Iban.Of(item.Iban),
                item.Amount,
                wageCalculatorService.Calculate(userConfig, item.Amount),
                item.NationalId,
                item.Mobile,
                item.Description);

            order.AddDetail(orderDetail);
            createdOrderDetails.Add(OrderDetailResponse.FromDomain(orderDetail, fullNameOrIban.Value));
        }

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        return result > 0
            ? new AddOrderDetailsResponse(createdOrderDetails)
            : Error.Failure(description: "خطا در ثبت اطلاعات");
    }

    private async Task<ErrorOr<string>> TryValidateIbanAndAccountStatus(string iban)
    {
        var inquiryIban = await ibanInquiryService.InquiryIban(iban);
        var fullNameOrIban = inquiryIban.Value?.AccountOwners.FirstOrDefault()?.FullName ?? iban;

        if (!inquiryIban.IsError && (!inquiryIban.Value?.IsActive ?? true))
            return Error.Forbidden(description:
                $"حساب بانکی مربوط به شبای {fullNameOrIban} مسدود می‌باشد");

        return fullNameOrIban;
    }

    private async Task<ErrorOr<Success>> ExceedDailyTransferLimitation(UserConfig userConfig, decimal amount, CancellationToken ct)
    {
        OrderDetailStatus[] statuses = [OrderDetailStatus.InProgress, OrderDetailStatus.Success];

        var totalAmount = await dbContext.OrderDetails
            .AsNoTracking()
            .Where(x => x.CreatedBy == currentUserService.UserId)
            .Where(x => x.Created >= dateTime.Now.Date && x.Created < dateTime.Now.AddDays(1).Date)
            .Where(x => statuses.Contains(x.Status))
            .SumAsync(x => x.Amount, ct);

        if (totalAmount + amount > userConfig.DailyTransferLimit)
            return Error.Conflict(description:
                string.Format(
                    ErrorMessages.MessageFormats.DailyTransferLimitExceeded,
                    amount.ToString("N0"),
                    userConfig.DailyTransferLimit.ToString("N0"),
                    totalAmount.ToString("N0")
                ));

        return Result.Success;
    }
}

public sealed class AddOrderDetailsCommandValidator : AbstractValidator<AddOrderDetailsCommand>
{
    private readonly IApplicationDbContext _dbContext;
    private readonly ICurrentUserService _currentUserService;

    public AddOrderDetailsCommandValidator(
        IApplicationDbContext dbContext,
        ICurrentUserService currentUserService,
        IDateTime dateTime)
    {
        _dbContext = dbContext;
        _currentUserService = currentUserService;

        RuleFor(x => x)
            .MustAsync(UserHasProPlan)
            .WithMessage(ErrorMessages.SimplePlanSettlementAdditionForbidden);

        RuleFor(x => x)
            .MustAsync(LessThanOrEqualMaximumOrderDetailsCount)
            .WithMessage(string.Format(
                ErrorMessages.MessageFormats.MaximumSettlementCountPerRequestExceeded,
                _currentUserService.GetMaxSettlementCountPerRequest));

        RuleFor(x => x.Items)
            .NotEmpty().WithMessage("لیست آیتم‌ها نمی‌تواند خالی باشد");

        RuleFor(x => x.OrderId).NotEmpty();

        RuleForEach(x => x.Items)
            .SetValidator(new OrderDetailItemValidator(dbContext, currentUserService));

        WhenAsync(UserIsCritical, () =>
            RuleForEach(x => x.Items)
                .SetValidator(new CriticalUserValidator(dbContext, currentUserService, dateTime)));
    }

    private async Task<bool> UserHasProPlan(AddOrderDetailsCommand _, CancellationToken ct)
    {
        var plan = await _dbContext.UserConfigs
            .AsNoTracking()
            .Where(x => x.UserId == _currentUserService.UserId)
            .Select(x => x.PlanType)
            .FirstOrDefaultAsync(ct);

        return plan == SettlementPlanType.Pro;
    }

    private async Task<bool> LessThanOrEqualMaximumOrderDetailsCount(AddOrderDetailsCommand command, CancellationToken ct)
    {
        var detailsCount = await _dbContext.OrderDetails
            .AsNoTracking()
            .Where(x => x.OrderId == command.OrderId)
            .CountAsync(ct);

        return detailsCount + command.Items.Count <= _currentUserService.GetMaxSettlementCountPerRequest;
    }

    private async Task<bool> UserIsCritical(AddOrderDetailsCommand _, CancellationToken ct)
    {
        return await _dbContext.UserConfigs
            .AsNoTracking()
            .Where(x => x.UserId == _currentUserService.UserId)
            .Select(x => x.IsCritical)
            .FirstOrDefaultAsync(ct);
    }
}

public class OrderDetailItemValidator : AbstractValidator<OrderDetailItem>
{
    public OrderDetailItemValidator(IApplicationDbContext dbContext, ICurrentUserService currentUserService)
    {
        RuleFor(x => x.Amount)
            .NotEmpty().WithMessage("مبلغ اجباری است")
            .GreaterThan(currentUserService.GetMinSettlementAmount)
            .WithMessage("مبلغ تسویه کمتر از حد مجاز")
            .MustAsync(LessThanMaxSettlementAmount)
            .WithMessage("مبلغ تسویه بیشتر از حد مجاز");

        RuleFor(x => x.Iban).NotEmpty();

        RuleFor(x => x.Description)
            .MaximumLength(50).WithMessage("متن توضیحات بیشتر از ۵۰ کارکتر نمی‌تواند باشد");
        return;

        async Task<bool> LessThanMaxSettlementAmount(decimal amount, CancellationToken ct)
        {
            long max = await dbContext.UserConfigs.AsNoTracking()
                .Where(x => x.UserId == currentUserService.UserId)
                .Select(x => x.MaxSettlementAmount)
                .FirstOrDefaultAsync(ct);
            if (max == 0) max = currentUserService.GetMaxSettlementAmount;
            return amount <= max;
        }
    }
}

public class CriticalUserValidator : AbstractValidator<OrderDetailItem>
{
    public CriticalUserValidator(
        IApplicationDbContext dbContext,
        ICurrentUserService currentUserService,
        IDateTime dateTime)
    {
        RuleFor(x => x.Mobile)
            .NotEmpty().WithMessage(ErrorMessages.MobileNumberRequired)
            .Must(x => x.IsValidIranianMobileNumber()).WithMessage(ErrorMessages.InvalidNationalCode);

        RuleFor(x => x.NationalId)
            .NotEmpty().WithMessage(ErrorMessages.NationalCodeRequired)
            .Must(x => x.IsValidIranianNationalCode()).WithMessage(ErrorMessages.InvalidNationalCode);

        RuleFor(x => x.Iban)
            .MustAsync(LessThanMaxTransferLimit).WithMessage(x =>
                $"امکان واریز بیش از 100میلیون تومان در ۲۴ساعت برای این شبا {x.Iban} نمی‌باشد");
        return;

        async Task<bool> LessThanMaxTransferLimit(OrderDetailItem request, string iban, CancellationToken ct)
        {
            OrderDetailStatus[] statuses = [OrderDetailStatus.InProgress, OrderDetailStatus.Success];
            var totalTransferAmount = await dbContext.OrderDetails
                .AsNoTracking()
                .Where(x => x.CreatedBy == currentUserService.UserId)
                .Where(x => x.Iban == iban)
                .Where(x => x.Created >= dateTime.Now.Date && x.Created < dateTime.Now.AddDays(1).Date)
                .Where(x => statuses.Contains(x.Status))
                .SumAsync(x => x.Amount, ct);

            return totalTransferAmount + request.Amount <= currentUserService.GetMaxLast24Amount;
        }
    }
}