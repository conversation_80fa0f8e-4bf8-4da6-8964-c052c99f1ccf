using ErrorOr;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Services.EzPay.Models;

namespace Zify.Settlement.Application.Infrastructure.Services.IbanInquiry;

public class EzPayIbanInquiryService(
    IEzPayService ezPayService)
    : IIbanInquiryService
{
    public async Task<ErrorOr<IbanInquiryResponse?>> InquiryIban(string iban)
    {
        var ezPayResult = await ezPayService.InquiryIban(iban);
        if (ezPayResult.IsError)
            return ezPayResult.Errors;

        return MapToGenericResponse(ezPayResult.Value);
    }

    public async Task<ErrorOr<List<IbanInquiryResponse>>> InquiryIbans(string[] ibanList)
    {
        var ezPayResult = await ezPayService.InquiryIbans(ibanList);
        if (ezPayResult.IsError)
            return ezPayResult.Errors;

        return ezPayResult.Value.ConvertAll(MapToGenericResponse);
    }

    private static IbanInquiryResponse MapToGenericResponse(
        EzPayInquiryIbanResponse? ezPayResponse)
    {
        if (ezPayResponse == null)
            return null!;

        return new IbanInquiryResponse(
            ezPayResponse.Iban,
            ezPayResponse.BankName,
            ezPayResponse.IsActive,
            ezPayResponse.AccountOwners.ConvertAll(owner =>
                new IbanAccountOwnerResponse(owner.FirstName, owner.LastName)));
    }
}
